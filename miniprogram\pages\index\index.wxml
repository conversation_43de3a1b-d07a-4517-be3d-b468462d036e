<!--index.wxml-->
<view class="container">
  <view class="title">快速了解云开发</view>
  <view class="top_tip">免鉴权接口调用 免部署后台 高并发</view>
  <view class="power" wx:key="title" wx:for="{{powerList}}" wx:for-item="power">
    <view class="power_info" data-index="{{index}}" data-type="{{ power.type }}" bindtap="onClickPowerInfo">
      <view class="power_info_text">
        <view class="power_info_text_title">
          {{power.title}}
          <view class="power_info_text_tag" wx:if="{{power.tag}}">{{power.tag}}</view>
        </view>
        <view class="power_info_text_tip">{{power.tip}}</view>
      </view>
      <image wx:if="{{!power.showItem && power.item.length}}" class="power_info_more" src="../../images/arrow.svg"></image>
      <image wx:if="{{power.showItem && power.item.length}}" class="power_info_less" src="../../images/arrow.svg"></image>
      <image wx:if="{{!power.item.length}}" class="power_item_icon" src="../../images/arrow.svg"></image>
    </view>
    <view wx:if="{{power.showItem}}">
      <view wx:key="title" wx:for="{{power.item}}">
        <view class="line"></view>
        <view class="power_item" bindtap="jumpPage" data-type="{{ item.type }}" data-page="{{item.page}}">
          <view class="power_item_title">{{item.title}}</view>
          <image class="power_item_icon" src="../../images/arrow.svg"></image>
        </view>
      </view>
    </view>
  </view>
  <cloud-tip-modal showTipProps="{{showTip}}" title="{{title}}" content="{{content}}"></cloud-tip-modal>
</view>